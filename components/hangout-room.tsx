"use client";

import { use<PERSON><PERSON>back, useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User } from "@prisma/client";
import {
    LiveKitRoom,
    useParticipants,
    RoomAudioRenderer,
    ControlBar,
    useIsRecording,
    useRoomContext
} from "@livekit/components-react";
import { useHangoutToken } from "@/hooks/use-hangout-token";
import { HangoutGridLayout } from "./hangout-grid-layout";
import { HangoutRecordingControls } from "./hangout-recording-controls";
import { useUser } from "@clerk/nextjs";
import { toast } from "sonner";
import { LogOut, X } from "lucide-react";
import { RoomEvent, DisconnectReason } from "livekit-client";

interface HangoutRoomProps {
    roomName: string;
    host: User;
    hangoutId: string;
}

// Inner component that uses <PERSON>Kit hooks
const HangoutRoomContent = ({
    roomName,
    host,
    hangoutId,
}: HangoutRoomProps) => {
    const router = useRouter();
    const participants = useParticipants();
    const { user } = useUser();
    const isRecording = useIsRecording();
    const room = useRoomContext();
    const [isEndingRoom, setIsEndingRoom] = useState(false);

    // Check if current user is the host
    const isHost = user?.id === host.externalUserId;

    // Handle room disconnection events
    useEffect(() => {
        if (!room) return;

        const handleDisconnected = (reason?: DisconnectReason) => {
            console.log(`[HANGOUT_DISCONNECT] Room disconnected: ${reason}`);
            // Redirect to hangouts page when disconnected
            router.push('/hangouts');
        };

        const handleReconnecting = () => {
            console.log('[HANGOUT_RECONNECTING] Attempting to reconnect...');
        };

        const handleReconnected = () => {
            console.log('[HANGOUT_RECONNECTED] Successfully reconnected');
            toast.success('Reconnected to hangout');
        };

        room.on(RoomEvent.Disconnected, handleDisconnected);
        room.on(RoomEvent.Reconnecting, handleReconnecting);
        room.on(RoomEvent.Reconnected, handleReconnected);

        return () => {
            room.off(RoomEvent.Disconnected, handleDisconnected);
            room.off(RoomEvent.Reconnecting, handleReconnecting);
            room.off(RoomEvent.Reconnected, handleReconnected);
        };
    }, [room, router]);

    // Handle browser close/refresh - cleanup participant status
    useEffect(() => {
        const handleBeforeUnload = () => {
            // Mark participant as left when browser is closed/refreshed
            if (user?.id) {
                navigator.sendBeacon('/api/hangout/leave', JSON.stringify({
                    hangoutId,
                    userId: user.id
                }));
            }
        };

        window.addEventListener('beforeunload', handleBeforeUnload);
        return () => window.removeEventListener('beforeunload', handleBeforeUnload);
    }, [hangoutId, user?.id]);

    const handleLeave = useCallback(async () => {
        try {
            // Mark participant as left in the database
            await fetch('/api/hangout/leave', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ hangoutId }),
            });
        } catch (error) {
            console.error('Failed to update leave status:', error);
            // Continue with navigation even if API call fails
        }

        // Disconnect from the room and navigate away
        if (room) {
            room.disconnect();
        }
        router.push('/hangouts');
    }, [router, hangoutId, room]);

    const handleEndRoom = useCallback(async () => {
        if (!isHost) return;

        setIsEndingRoom(true);
        try {
            const response = await fetch('/api/hangout/end', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ hangoutId }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to end hangout');
            }

            toast.success('Hangout ended successfully');

            // Disconnect from the room
            if (room) {
                room.disconnect();
            }

            router.push('/hangouts');

        } catch (error) {
            console.error('Failed to end hangout:', error);
            toast.error(error instanceof Error ? error.message : 'Failed to end hangout');
        } finally {
            setIsEndingRoom(false);
        }
    }, [isHost, hangoutId, router, room]);

    return (
        <div className="flex flex-col h-screen bg-black text-white">
            {/* Header - Fixed height */}
            <div className="flex items-center justify-between p-4 bg-gray-900/50 backdrop-blur shrink-0">
                <div className="flex items-center space-x-3">
                    <Avatar className="w-8 h-8">
                        <AvatarImage src={host.imageURL} />
                        <AvatarFallback>{host.username.charAt(0).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <div>
                        <h1 className="font-semibold">{roomName}</h1>
                        <p className="text-sm text-gray-400">
                            {participants.length} people here
                            {isRecording && (
                                <span className="ml-2 inline-flex items-center space-x-1 text-red-400">
                                    <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                                    <span className="text-xs">Recording</span>
                                </span>
                            )}
                        </p>
                    </div>
                </div>

                <div className="flex items-center space-x-2">
                    {/* Recording Controls - Only for host */}
                    {isHost && (
                        <HangoutRecordingControls
                            hangoutId={hangoutId}
                            hostUserId={host.externalUserId}
                        />
                    )}

                    {/* End Room Button - Only for host */}
                    {isHost && (
                        <Button
                            variant="destructive"
                            size="sm"
                            onClick={handleEndRoom}
                            disabled={isEndingRoom}
                            className="flex items-center space-x-2"
                        >
                            <X className="h-4 w-4" />
                            <span>{isEndingRoom ? 'Ending...' : 'End Room'}</span>
                        </Button>
                    )}

                    {/* Leave Button - For all participants */}
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleLeave}
                        className="text-white hover:bg-gray-800 flex items-center space-x-2"
                    >
                        <LogOut className="h-4 w-4" />
                        <span>Leave</span>
                    </Button>
                </div>
            </div>

            {/* Main video grid - Takes remaining space between header and controls */}
            <div className="flex-1 min-h-0 overflow-hidden">
                <HangoutGridLayout />
            </div>

            {/* Controls - Fixed height */}
            <div className="flex items-center justify-center space-x-4 p-6 bg-gray-900/50 backdrop-blur shrink-0">
                <ControlBar
                    variation="minimal"
                    controls={{
                        microphone: true,
                        camera: true,
                        screenShare: isHost, // Only host can screen share
                        leave: false, // We handle leave in the header
                    }}
                />
            </div>

            {/* Audio renderer for all participants */}
            <RoomAudioRenderer />
        </div>
    );
};

// Main component wrapper with LiveKit room
export const HangoutRoom = ({
    roomName,
    host,
    hangoutId,
}: HangoutRoomProps) => {
    const { token, name, identity } = useHangoutToken(hangoutId);

    if (!token || !name || !identity) {
        return (
            <div className="flex items-center justify-center h-screen bg-black text-white">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
                    <p>Connecting to hangout...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="h-screen w-full" data-lk-theme="default">
            <LiveKitRoom
                token={token}
                serverUrl={process.env.NEXT_PUBLIC_LIVEKIT_WS_URL!}
                connect={true}
                audio={true}
                video={true}
                className="h-full w-full"
            >
                <HangoutRoomContent
                    roomName={roomName}
                    host={host}
                    hangoutId={hangoutId}
                />
            </LiveKitRoom>
        </div>
    );
};