import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { EgressClient, EncodingOptionsPreset, EncodedOutputs, EncodedFileOutput, SegmentedFileOutput } from "livekit-server-sdk";

const egressClient = new EgressClient(
    process.env.LIVEKIT_API_URL!,
    process.env.LIVEKIT_API_KEY!,
    process.env.LIVEKIT_API_SECRET!
);

export async function POST(req: NextRequest) {
    try {
        const { hangoutId, title, description } = await req.json();

        if (!hangoutId) {
            return NextResponse.json({ error: "Hangout ID is required" }, { status: 400 });
        }

        // Get authenticated user
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Get user from database
        const user = await db.user.findUnique({
            where: { externalUserId: userId },
        });

        if (!user) {
            return NextResponse.json({ error: "User not found" }, { status: 404 });
        }

        // Get hangout room and verify user is the host
        const hangoutRoom = await db.hangoutRoom.findUnique({
            where: { id: hangoutId },
            include: {
                host: true,
            },
        });

        if (!hangoutRoom) {
            return NextResponse.json({ error: "Hangout room not found" }, { status: 404 });
        }

        // Only the host can start recording
        if (hangoutRoom.hostUserId !== user.id) {
            return NextResponse.json({ error: "Only the host can start recording" }, { status: 403 });
        }

        // Check if room is active
        if (hangoutRoom.status !== "ACTIVE") {
            return NextResponse.json({ error: "Cannot record inactive hangout room" }, { status: 400 });
        }

        // Create a unique filename for the recording
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `hangout-${hangoutId}-${timestamp}`;
        const fullPath = `recordings/hangouts/${filename}`;

        // Configure outputs for Cloudflare R2 (same as existing recording system)
        const outputs: EncodedOutputs = {
            // File output for MP4 recording
            file: new EncodedFileOutput({
                filepath: `${fullPath}.mp4`,
                output: {
                    case: 's3',
                    value: {
                        endpoint: process.env.CLOUDFLARE_R2_ENDPOINT!,
                        bucket: process.env.CLOUDFLARE_R2_BUCKET!,
                        accessKey: process.env.CLOUDFLARE_R2_ACCESS_KEY!,
                        secret: process.env.CLOUDFLARE_R2_SECRET_KEY!,
                        region: 'auto',
                        forcePathStyle: true,
                    },
                },
            }),
            // Segmented output for HLS streaming
            segments: new SegmentedFileOutput({
                filenamePrefix: fullPath,
                playlistName: `${fullPath}.m3u8`,
                livePlaylistName: `${fullPath}-live.m3u8`,
                segmentDuration: 6,
                output: {
                    case: 's3',
                    value: {
                        endpoint: process.env.CLOUDFLARE_R2_ENDPOINT!,
                        bucket: process.env.CLOUDFLARE_R2_BUCKET!,
                        accessKey: process.env.CLOUDFLARE_R2_ACCESS_KEY!,
                        secret: process.env.CLOUDFLARE_R2_SECRET_KEY!,
                        region: 'auto',
                        forcePathStyle: true,
                    },
                },
            }),
        };

        // Custom base URL for recording template
        const customBaseUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/recording-template`;

        console.log(`[HANGOUT_RECORDING_START] Starting recording for hangout ${hangoutId}`);
        console.log(`[HANGOUT_RECORDING_START] Room name: hangout-${hangoutId}`);
        console.log(`[HANGOUT_RECORDING_START] Custom base URL: ${customBaseUrl}`);

        // Start the egress with hangout-specific room name
        const egressInfo = await egressClient.startRoomCompositeEgress(
            `hangout-${hangoutId}`, // Use the hangout-specific LiveKit room name
            outputs,
            {
                layout: 'hangout-grid', // Custom layout identifier for hangouts
                encodingOptions: EncodingOptionsPreset.PORTRAIT_H264_720P_30, // Use portrait preset for 9:16
                audioOnly: false,
                customBaseUrl: customBaseUrl
            }
        );

        // Create database record for the recording session
        // Note: We'll need to create a HangoutRecordedSession model or adapt the existing one
        const recordedSession = await db.recordedSession.create({
            data: {
                streamId: hangoutRoom.id, // Using hangout ID as stream ID for now
                hostUserId: user.id,
                title: title || `${user.username}'s Hangout`,
                description: description || null,
                egressId: egressInfo.egressId,
                videoUrl: `${fullPath}.mp4`,
                hlsUrl: `${fullPath}.m3u8`,
                recordingStartedAt: new Date(),
                processingStatus: 'PROCESSING',
            },
        });

        console.log(`[HANGOUT_RECORDING_START] Recording started for hangout ${hangoutId}, egress ID: ${egressInfo.egressId}`);

        return NextResponse.json({
            success: true,
            sessionId: recordedSession.id,
            egressId: egressInfo.egressId,
            hangoutId,
            message: "Recording started successfully"
        });

    } catch (error) {
        console.error("Failed to start hangout recording:", error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : "Failed to start hangout recording" },
            { status: 500 }
        );
    }
}
